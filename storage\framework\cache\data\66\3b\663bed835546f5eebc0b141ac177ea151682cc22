1754307404O:32:"App\Models\RolePermissionMapping":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:23:"role_permission_mapping";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:60;s:7:"menu_id";i:32;s:7:"role_id";i:4;s:6:"canAdd";i:1;s:7:"canView";i:1;s:7:"canEdit";i:1;s:9:"canDelete";i:1;s:10:"created_at";s:19:"2025-07-28 09:25:34";s:10:"updated_at";s:19:"2025-08-04 11:23:59";s:9:"menu_name";s:13:"Customer List";}s:11:" * original";a:10:{s:2:"id";i:60;s:7:"menu_id";i:32;s:7:"role_id";i:4;s:6:"canAdd";i:1;s:7:"canView";i:1;s:7:"canEdit";i:1;s:9:"canDelete";i:1;s:10:"created_at";s:19:"2025-07-28 09:25:34";s:10:"updated_at";s:19:"2025-08-04 11:23:59";s:9:"menu_name";s:13:"Customer List";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:7:"menu_id";i:1;s:7:"role_id";i:2;s:6:"canAdd";i:3;s:7:"canView";i:4;s:7:"canEdit";i:5;s:9:"canDelete";i:6;s:10:"created_at";i:7;s:10:"updated_at";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}