<?php $__env->startSection('content'); ?>
    <div class="container-fluid flex-grow-1 container-p-y">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div class="head-label text-center">
                    <h5 class="card-title mb-0"><b><?php echo e($menuName); ?></b></h5>
                </div>
                <?php if($permissions['canAdd']): ?>
                    <button id="btnAdd" type="submit" class="btn btn-primary waves-effect waves-light"
                        onClick="fnAddEdit(this, '<?php echo e(url('/installers/create')); ?>', 0, 'Add Installer')">
                        <span class="tf-icons mdi mdi-plus">&nbsp;</span>Add Installer
                    </button>
                <?php endif; ?>
            </div>
            <hr class="my-0">
            <div class="card-datatable text-nowrap">
                <table id="grid" class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Action</th>
                            <th>Installer Name</th>
                            <th>Installer Phone</th>
                            <th>Installer Email</th>
                            <th>Installer Address</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>

    <script type="text/javascript">
        $(document).ready(function() {
            initializeDataTable();
        });

        function initializeDataTable() {
            $("#grid").DataTable({
                responsive: true,
                autoWidth: false,
                serverSide: false,
                processing: true,
                'language': {
                    "loadingRecords": "&nbsp;",
                    "processing": "<img src='<?php echo e(asset('assets/img/illustrations/loader.gif')); ?>' alt='loader' />"
                },
                order: [
                    [1, "desc"]
                ],
                ajax: {
                    url: "<?php echo e(config('apiConstants.INSTALLERS_URLS.INSTALLERS')); ?>",
                    type: "GET",
                    headers: {
                        Authorization: "Bearer " + getCookie("access_token"),
                    },
                },
                columns: [{
                        data: "id",
                        orderable: false,
                        render: function(data, type, row) {
                            var html = "<ul class='list-inline m-0'>";

                            html += "<li class='list-inline-item'>" +
                                GetEditDeleteButton(<?php echo e($permissions['canEdit']); ?>,
                                    "<?php echo e(url('installers/create')); ?>", "Edit",
                                    data, "Edit Installer") +
                                "</li>";

                            html += "<li class='list-inline-item'>" +
                                GetEditDeleteButton(<?php echo e($permissions['canDelete']); ?>,
                                    "fnShowConfirmDeleteDialog('" + row.name + "',fnDeleteRecord," +
                                    data + ",'" +
                                    '<?php echo e(config('apiConstants.INSTALLERS_URLS.INSTALLERS_DELETE')); ?>' +
                                    "','#grid')", "Delete") +
                                "</li>";

                            html += "</ul>";
                            return html;
                        },
                    },
                    {
                        data: "name",
                    },
                    {
                        data: "phone",
                    },
                    {
                        data: "email",
                    },
                    {
                        data: "address",
                    }
                ]
            });
        }
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\INCT\Parth\practice projects\RJ_ENERGY\resources\views/installers/installers_index.blade.php ENDPATH**/ ?>