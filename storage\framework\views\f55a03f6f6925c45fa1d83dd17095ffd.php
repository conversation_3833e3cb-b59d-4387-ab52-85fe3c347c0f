<?php $__env->startSection('content'); ?>
    <div class="container-fluid flex-grow-1 container-p-y">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div class="head-label text-center">
                    <h5 class="card-title mb-0"><b><?php echo e($menuName); ?></b></h5>
                </div>
                <?php if($permissions['canAdd']): ?>
                    <button id="btnAdd" type="submit" class="btn btn-primary waves-effect waves-light"
                        onClick="fnAddEdit(this, '<?php echo e(url('manage-bank/create')); ?>', 0, 'Add Bank')">
                        <span class="tf-icons mdi mdi-plus">&nbsp;</span>Add Bank
                    </button>
                <?php endif; ?>
            </div>
            <hr class="my-0">
            <div class="card-datatable text-nowrap">
                <table id="grid" class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Action</th>
                            <th>Bank Name</th>
                            <th>Branch Name</th>
                            <th>Branch Manager Phone</th>
                            <th>Loan Manager Phone</th>
                            <th>IFSC Code</th>
                            <th>Branch Address</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>

    <script type="text/javascript">
        $(document).ready(function() {
            initializeDataTable();
        });

        function initializeDataTable() {
            $("#grid").DataTable({
                responsive: true,
                autoWidth: false,
                serverSide: false,
                processing: true,
                'language': {
                    "loadingRecords": "&nbsp;",
                    "processing": "<img src='<?php echo e(asset('assets/img/illustrations/loader.gif')); ?>' alt='loader' />"
                },
                order: [
                    [1, "desc"]
                ],
                ajax: {
                    url: "<?php echo e(config('apiConstants.MANAGE_BANK_URLS.MANAGE_BANK')); ?>",
                    type: "GET",
                    headers: {
                        Authorization: "Bearer " + getCookie("access_token"),
                    },
                },
                columns: [{
                        data: "id",
                        orderable: false,
                        render: function(data, type, row) {
                            var html = "<ul class='list-inline m-0'>";

                            // Edit Button (This is your existing edit button logic)
                            html += "<li class='list-inline-item'>" +
                                GetEditDeleteButton(<?php echo e($permissions['canEdit']); ?>,
                                    "<?php echo e(url('manage-bank/create')); ?>", "Edit",
                                    data, "Edit Bank") +
                                "</li>";

                            // Delete Button
                            html += "<li class='list-inline-item'>" +
                                GetEditDeleteButton(<?php echo e($permissions['canDelete']); ?>,
                                    "fnShowConfirmDeleteDialog('" + row.bank_name + "',fnDeleteRecord," +
                                    data + ",'" +
                                    '<?php echo e(config('apiConstants.MANAGE_BANK_URLS.MANAGE_BANK_DELETE')); ?>' +
                                    "','#grid')", "Delete") +
                                "</li>";

                            html += "</ul>";
                            return html;
                        },
                    },
                    {
                        data: "bank_name",
                    },
                    {
                        data: "branch_name",
                    },
                    {
                        data: "branch_manager_phone",
                    },
                    {
                        data: "loan_manager_phone",
                    },
                    {
                        data: "ifsc_code",
                    },
                    {
                        data: "address",
                    }
                ]
            });
        }
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\INCT\Parth\practice projects\RJ_ENERGY\resources\views/manageBank/manageBank_index.blade.php ENDPATH**/ ?>